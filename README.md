# MERN Stack Blog Application

A full-stack blog application built with MongoDB, Express.js, React, and Node.js.

## Features

### Backend Features
- RESTful API with Express.js
- MongoDB database with Mongoose ODM
- JWT-based authentication
- User registration and login
- Blog post CRUD operations
- Image upload functionality
- Input validation and error handling
- Secure password hashing

### Frontend Features
- Modern React application with hooks
- React Router for navigation
- Responsive design
- User authentication forms
- Blog post creation and editing
- User dashboard and profile management
- Search and filtering capabilities
- Pagination for blog posts
- Context API for state management

## Project Structure

```
blog/
├── backend/                 # Node.js/Express server
│   ├── config/             # Database and app configuration
│   ├── controllers/        # Route controllers
│   ├── middleware/         # Custom middleware
│   ├── models/            # Mongoose models
│   ├── routes/            # API routes
│   ├── uploads/           # File upload directory
│   ├── utils/             # Utility functions
│   ├── .env               # Environment variables
│   ├── package.json       # Backend dependencies
│   └── server.js          # Main server file
├── frontend/              # React application
│   ├── public/            # Static files
│   ├── src/               # React source code
│   │   ├── components/    # Reusable components
│   │   ├── pages/         # Page components
│   │   ├── context/       # Context providers
│   │   ├── services/      # API services
│   │   ├── styles/        # CSS files
│   │   ├── utils/         # Utility functions
│   │   ├── App.js         # Main App component
│   │   └── index.js       # Entry point
│   ├── package.json       # Frontend dependencies
│   └── .env               # Frontend environment variables
├── .gitignore             # Git ignore file
└── README.md              # Project documentation
```

## Getting Started

### Prerequisites
- Node.js (v14 or higher)
- MongoDB (local installation or MongoDB Atlas)
- npm or yarn package manager

### Installation

1. Clone the repository
2. Install backend dependencies:
   ```bash
   cd backend
   npm install
   ```

3. Install frontend dependencies:
   ```bash
   cd frontend
   npm install
   ```

4. Set up environment variables (see .env.example files)

5. Start the development servers:
   ```bash
   # Backend (runs on port 5000)
   cd backend
   npm run dev

   # Frontend (runs on port 3000)
   cd frontend
   npm start
   ```

## API Endpoints

### Authentication
- POST `/api/auth/register` - User registration
- POST `/api/auth/login` - User login
- POST `/api/auth/logout` - User logout
- GET `/api/auth/me` - Get current user

### Blog Posts
- GET `/api/posts` - Get all blog posts (with pagination)
- GET `/api/posts/:id` - Get single blog post
- POST `/api/posts` - Create new blog post (authenticated)
- PUT `/api/posts/:id` - Update blog post (authenticated, author only)
- DELETE `/api/posts/:id` - Delete blog post (authenticated, author only)

### User Profile
- GET `/api/users/profile` - Get user profile
- PUT `/api/users/profile` - Update user profile

## Technologies Used

### Backend
- Node.js
- Express.js
- MongoDB with Mongoose
- JWT for authentication
- bcryptjs for password hashing
- multer for file uploads
- express-validator for input validation

### Frontend
- React 18
- React Router DOM
- Context API for state management
- Axios for API calls
- CSS Modules for styling

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License.
